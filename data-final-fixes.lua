-- data-final-fixes.lua
-- Enable quality effects on mining drills and adjust base stats to reach target values

-- Get setting values safely
local function get_setting(name, default)
    local setting = settings.startup[name]
    return setting and setting.value or default
end

-- Function to enable quality effects and modify base stats
local function setup_miner_quality(miner_name)
    local miner = data.raw["mining-drill"][miner_name]
    if not miner then
        log("Warning: Mining drill '" .. miner_name .. "' not found")
        return
    end

    -- Enable quality effects on mining radius (this gives the blue diamond indicator)
    miner.quality_affects_mining_radius = true

    -- Enable quality effects on module slots (but only for drills that support modules)
    if miner_name ~= "burner-mining-drill" then
        miner.quality_affects_module_slots = true
    end

    -- Get target multipliers for legendary quality
    local legendary_speed_target = get_setting("cmq-speed-legendary", 3.0)
    local legendary_range_target = get_setting("cmq-range-legendary", 2.5)
    local legendary_power_target = get_setting("cmq-power-legendary", 2.0)
    local legendary_module_bonus = get_setting("cmq-modules-legendary", 3)

    -- Quality system gives +30% per tier, legendary is 5 tiers = +150% = 2.5x multiplier
    -- To reach our target legendary value: base * 2.5 = target, so base = target / 2.5
    local quality_multiplier = 2.5

    -- Store and modify base values
    local original_speed = miner.mining_speed or 1
    local original_range = miner.resource_searching_radius or 1
    local original_power = miner.energy_usage
    local original_modules = miner.module_slots or 0

    -- Parse power value
    local original_power_value = original_power
    if type(original_power) == "string" then
        original_power_value = tonumber(original_power:match("(%d+%.?%d*)")) or 300
    end

    -- Calculate and apply new base values
    miner.mining_speed = (original_speed * legendary_speed_target) / quality_multiplier
    miner.resource_searching_radius = (original_range * legendary_range_target) / quality_multiplier
    miner.energy_usage = ((original_power_value * legendary_power_target) / quality_multiplier) .. "kW"

    -- Handle module slots (only for non-burner drills)
    if miner_name ~= "burner-mining-drill" and legendary_module_bonus > 0 then
        -- Set base module slots so that legendary reaches our target
        local target_legendary_modules = original_modules + legendary_module_bonus
        -- Quality doesn't multiply module slots, it adds a fixed bonus per tier
        -- So we need to set the base appropriately
        miner.module_slots = math.max(original_modules, 1) -- Ensure at least 1 slot for quality to work with

        -- Enable modules if not already enabled
        if not miner.allowed_effects then
            miner.allowed_effects = {"consumption", "speed", "productivity", "pollution", "quality"}
        end
    end

    log("Setup quality effects for " .. miner_name .. ":")
    log("  Speed: " .. original_speed .. " -> " .. miner.mining_speed .. " (target legendary: " .. (miner.mining_speed * quality_multiplier) .. ")")
    log("  Range: " .. original_range .. " -> " .. miner.resource_searching_radius .. " (target legendary: " .. (miner.resource_searching_radius * quality_multiplier) .. ")")
    log("  Power: " .. original_power_value .. "kW -> " .. ((original_power_value * legendary_power_target) / quality_multiplier) .. "kW")
    log("  Quality affects radius: " .. tostring(miner.quality_affects_mining_radius))
    log("  Quality affects modules: " .. tostring(miner.quality_affects_module_slots or false))
end

-- Apply quality setup to all mining drills
local miners_to_modify = {
    "burner-mining-drill",
    "electric-mining-drill",
    "big-mining-drill"
}

for _, miner_name in pairs(miners_to_modify) do
    setup_miner_quality(miner_name)
end

log("Custom Miner Quality: Enabled quality effects for all mining drills")
