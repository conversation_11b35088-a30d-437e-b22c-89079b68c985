-- data-final-fixes.lua
-- Enable quality effects on mining drills with custom multipliers

-- Get setting values safely
local function get_setting(name, default)
    local setting = settings.startup[name]
    return setting and setting.value or default
end

-- Function to setup custom quality effects for mining drills
local function setup_miner_quality(miner_name)
    local miner = data.raw["mining-drill"][miner_name]
    if not miner then
        log("Warning: Mining drill '" .. miner_name .. "' not found")
        return
    end

    -- Enable quality effects on energy usage and mining radius
    miner.quality_affects_energy_usage = true
    miner.quality_affects_mining_radius = true

    -- Enable quality effects on module slots (but only for drills that support modules)
    if miner_name ~= "burner-mining-drill" then
        miner.quality_affects_module_slots = true

        -- Ensure modules are allowed
        if not miner.allowed_effects then
            miner.allowed_effects = {"consumption", "speed", "productivity", "pollution", "quality"}
        end
    end

    -- Get custom multipliers from settings
    local speed_multipliers = {
        uncommon = get_setting("cmq-speed-uncommon", 1.25),
        rare = get_setting("cmq-speed-rare", 1.5),
        epic = get_setting("cmq-speed-epic", 2.0),
        legendary = get_setting("cmq-speed-legendary", 3.0)
    }

    local power_multipliers = {
        uncommon = get_setting("cmq-power-uncommon", 1.1),
        rare = get_setting("cmq-power-rare", 1.3),
        epic = get_setting("cmq-power-epic", 1.6),
        legendary = get_setting("cmq-power-legendary", 2.0)
    }

    -- Set custom quality multipliers for crafting speed (mining speed)
    miner.crafting_speed_quality_multiplier = speed_multipliers

    -- Set custom quality multipliers for energy usage (power consumption)
    miner.energy_usage_quality_multiplier = power_multipliers

    -- For range, we need to adjust the base value since there's no custom multiplier option
    -- We'll scale it so legendary reaches our target
    local legendary_range_target = get_setting("cmq-range-legendary", 2.5)
    local original_range = miner.resource_searching_radius or 1

    -- Quality system gives +30% per tier for range, legendary = 2.5x
    -- To reach our target: base * 2.5 = target, so base = target / 2.5
    miner.resource_searching_radius = (original_range * legendary_range_target) / 2.5

    -- Handle module slots for non-burner drills
    if miner_name ~= "burner-mining-drill" then
        local module_bonuses = {
            uncommon = get_setting("cmq-modules-uncommon", 0),
            rare = get_setting("cmq-modules-rare", 1),
            epic = get_setting("cmq-modules-epic", 2),
            legendary = get_setting("cmq-modules-legendary", 3)
        }

        -- Set custom module slot bonuses
        miner.module_slots_quality_bonus = module_bonuses

        -- Ensure we have at least some base module slots
        local original_modules = miner.module_slots or 0
        if original_modules == 0 and module_bonuses.legendary > 0 then
            miner.module_slots = 1 -- Give at least 1 base slot so quality can add more
        end
    end

    log("Setup custom quality effects for " .. miner_name .. ":")
    log("  Speed multipliers: uncommon=" .. speed_multipliers.uncommon .. ", rare=" .. speed_multipliers.rare .. ", epic=" .. speed_multipliers.epic .. ", legendary=" .. speed_multipliers.legendary)
    log("  Power multipliers: uncommon=" .. power_multipliers.uncommon .. ", rare=" .. power_multipliers.rare .. ", epic=" .. power_multipliers.epic .. ", legendary=" .. power_multipliers.legendary)
    log("  Range: " .. (original_range or "unknown") .. " -> " .. miner.resource_searching_radius .. " (legendary target: " .. legendary_range_target .. "x)")
    log("  Quality affects: energy=" .. tostring(miner.quality_affects_energy_usage) .. ", radius=" .. tostring(miner.quality_affects_mining_radius) .. ", modules=" .. tostring(miner.quality_affects_module_slots or false))
end

-- Apply quality setup to all mining drills
local miners_to_modify = {
    "burner-mining-drill",
    "electric-mining-drill",
    "big-mining-drill"
}

for _, miner_name in pairs(miners_to_modify) do
    setup_miner_quality(miner_name)
end

log("Custom Miner Quality: Applied custom quality multipliers to all mining drills")
