-- data-final-fixes.lua
-- Apply custom scaling to mining drill base stats
-- The quality system will then apply its standard +30% per tier to these modified base values

-- Get setting values safely
local function get_setting(name, default)
    local setting = settings.startup[name]
    return setting and setting.value or default
end

-- Function to modify base stats of mining drills
local function modify_miner_base_stats(miner_name)
    local miner = data.raw["mining-drill"][miner_name]
    if not miner then
        log("Warning: Mining drill '" .. miner_name .. "' not found")
        return
    end

    -- Get the legendary multipliers to scale base stats
    -- We'll scale the base stats so that legendary quality reaches our target values
    local legendary_speed_target = get_setting("cmq-speed-legendary", 3.0)
    local legendary_range_target = get_setting("cmq-range-legendary", 2.5)
    local legendary_power_target = get_setting("cmq-power-legendary", 2.0)

    -- Quality gives +30% per tier, legendary is 5 tiers = +150% = 2.5x multiplier
    -- So to get our target legendary value, we need: base * 2.5 = target
    -- Therefore: base = target / 2.5
    local quality_multiplier = 2.5  -- Default quality system multiplier for legendary

    -- Store original values
    local original_speed = miner.mining_speed or 1
    local original_range = miner.resource_searching_radius or 1
    local original_power = miner.energy_usage

    -- Parse power value
    local original_power_value = original_power
    if type(original_power) == "string" then
        original_power_value = tonumber(original_power:match("(%d+%.?%d*)")) or 300
    end

    -- Calculate new base values to achieve our legendary targets
    local new_base_speed = (original_speed * legendary_speed_target) / quality_multiplier
    local new_base_range = (original_range * legendary_range_target) / quality_multiplier
    local new_base_power = (original_power_value * legendary_power_target) / quality_multiplier

    -- Apply new base values
    miner.mining_speed = new_base_speed
    miner.resource_searching_radius = new_base_range
    miner.energy_usage = new_base_power .. "kW"

    -- For module slots, we'll add a base bonus and let quality add more
    -- Since quality doesn't naturally affect module slots for miners, we'll use a different approach
    local legendary_module_bonus = get_setting("cmq-modules-legendary", 3)
    if legendary_module_bonus > 0 then
        local original_modules = miner.module_slots or 0
        -- Add some base module slots that will be enhanced by quality
        miner.module_slots = original_modules + math.floor(legendary_module_bonus / 2)
    end

    log("Modified base stats for " .. miner_name .. ":")
    log("  Speed: " .. original_speed .. " -> " .. new_base_speed)
    log("  Range: " .. original_range .. " -> " .. new_base_range)
    log("  Power: " .. original_power_value .. "kW -> " .. new_base_power .. "kW")
    log("  Modules: " .. (miner.module_slots or 0))
end

-- Apply modifications to all mining drills
local miners_to_modify = {
    "burner-mining-drill",
    "electric-mining-drill",
    "big-mining-drill"
}

for _, miner_name in pairs(miners_to_modify) do
    modify_miner_base_stats(miner_name)
end

log("Custom Miner Quality: Modified base stats for all mining drills")
