# Custom Miner Quality

A Factorio Space Age mod that enhances mining drills with quality-based improvements.

## Features

This mod allows you to customize how quality affects mining drills across all quality tiers:

- **Speed Multipliers**: Increase mining speed for higher quality drills
- **Range Multipliers**: Expand resource searching radius for better coverage
- **Power Multipliers**: Adjust power consumption (can increase or decrease)
- **Module Slot Bonuses**: Add extra module slots to higher quality drills

## Affected Mining Drills

- Burner Mining Drill
- Electric Mining Drill  
- Big Mining Drill

## Configuration

All settings are available in the game's mod settings menu under "Startup" settings:

### Speed Multipliers
- Uncommon: 1.25x (default)
- Rare: 1.5x (default)
- Epic: 2.0x (default)
- Legendary: 3.0x (default)

### Range Multipliers
- Uncommon: 1.2x (default)
- Rare: 1.5x (default)
- Epic: 2.0x (default)
- Legendary: 2.5x (default)

### Power Multipliers
- Uncommon: 1.1x (default)
- Rare: 1.3x (default)
- Epic: 1.6x (default)
- Legendary: 2.0x (default)

### Module Slot Bonuses
- Uncommon: +0 slots (default)
- Rare: +1 slot (default)
- Epic: +2 slots (default)
- Legendary: +3 slots (default)

## How It Works

The mod modifies the base stats of mining drills so that when Factorio's quality system applies its standard bonuses, the final values match your configured targets. The quality system naturally provides +30% per quality tier, and this mod adjusts the base values to achieve your desired legendary-tier performance.

**Important**: The intermediate quality tiers (uncommon, rare, epic) will have values that scale proportionally between normal and legendary based on Factorio's standard quality progression.

## Installation

1. Download the mod
2. Place it in your Factorio mods folder
3. Enable it in the game
4. Configure settings in Options > Mods > Startup

## Compatibility

- Requires Factorio 2.0+
- Compatible with other mods that add mining drills
- Settings require a game restart to take effect
