-- settings-final-fixes.lua
-- Apply mod settings to mining drill prototypes after all mods have loaded

-- Get setting values safely
local function get_setting(name, default)
    local setting = settings.startup[name]
    return setting and setting.value or default
end

-- Quality tiers and their corresponding quality levels
local quality_data = {
    {name = "uncommon", level = 2},
    {name = "rare", level = 3},
    {name = "epic", level = 4},
    {name = "legendary", level = 5}
}

-- Function to apply quality effects to a mining drill
local function setup_miner_quality_effects(miner_name)
    local miner = data.raw["mining-drill"][miner_name]
    if not miner then 
        log("Warning: Mining drill '" .. miner_name .. "' not found")
        return 
    end
    
    -- Store base values for calculations
    local base_speed = miner.mining_speed or 1
    local base_range = miner.resource_searching_radius or 1
    local base_power = miner.energy_usage
    local base_modules = miner.module_slots or 0
    
    -- Parse base power consumption (remove "kW" suffix if present)
    local base_power_value = base_power
    if type(base_power) == "string" then
        base_power_value = tonumber(base_power:match("(%d+%.?%d*)")) or 300
    end
    
    -- Initialize quality_modifiers table
    if not miner.quality_modifiers then
        miner.quality_modifiers = {}
    end
    
    -- Apply modifiers for each quality tier
    for _, quality in pairs(quality_data) do
        local tier = quality.name
        
        -- Get multipliers from settings
        local speed_mult = get_setting("cmq-speed-" .. tier, 1.0)
        local range_mult = get_setting("cmq-range-" .. tier, 1.0)
        local power_mult = get_setting("cmq-power-" .. tier, 1.0)
        local module_bonus = get_setting("cmq-modules-" .. tier, 0)
        
        -- Create quality modifier entry
        miner.quality_modifiers[tier] = {}
        
        -- Apply speed modifier
        if speed_mult ~= 1.0 then
            miner.quality_modifiers[tier].mining_speed = base_speed * speed_mult
        end
        
        -- Apply range modifier
        if range_mult ~= 1.0 then
            miner.quality_modifiers[tier].resource_searching_radius = base_range * range_mult
        end
        
        -- Apply power modifier
        if power_mult ~= 1.0 then
            local new_power = base_power_value * power_mult
            miner.quality_modifiers[tier].energy_usage = new_power .. "kW"
        end
        
        -- Apply module slot bonus
        if module_bonus > 0 then
            miner.quality_modifiers[tier].module_slots = base_modules + module_bonus
        end
        
        log("Applied quality modifiers for " .. miner_name .. " (" .. tier .. "): speed=" .. speed_mult .. ", range=" .. range_mult .. ", power=" .. power_mult .. ", modules=+" .. module_bonus)
    end
end

-- Apply quality effects to all mining drills
local miners_to_modify = {
    "burner-mining-drill",
    "electric-mining-drill", 
    "big-mining-drill"
}

for _, miner_name in pairs(miners_to_modify) do
    setup_miner_quality_effects(miner_name)
end

log("Custom Miner Quality: Applied quality modifiers to all mining drills")
