-- data.lua
local stats = require("editable_stats")

local function apply_stats(miner_name, miner_table)
    local miner = data.raw["mining-drill"][miner_name]
    if not miner then return end

    for tier, values in pairs(miner_table) do
        -- Make a copy of the miner prototype for each quality tier
        local miner_copy = table.deepcopy(miner)
        miner_copy.name = miner.name .. "-" .. tier
        miner_copy.mining_speed = values.speed
        miner_copy.resource_searching_radius = values.range
        miner_copy.energy_usage = values.power .. "kW"
        if values.pollution then
            miner_copy.pollution = values.pollution
        end
        data:extend({miner_copy})
    end
end

-- Apply stats for each miner type
apply_stats("burner-mining-drill", stats.burner)
apply_stats("electric-mining-drill", stats.electric)
apply_stats("big-mining-drill", stats.big)

