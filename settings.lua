-- settings.lua
-- Define mod settings that appear in the game's mod settings menu

data:extend({
  -- Speed multipliers for each quality tier
  {
    type = "double-setting",
    name = "cmq-speed-uncommon",
    setting_type = "startup",
    default_value = 1.25,
    minimum_value = 0.1,
    maximum_value = 10.0,
    order = "a-speed-01"
  },
  {
    type = "double-setting",
    name = "cmq-speed-rare",
    setting_type = "startup",
    default_value = 1.5,
    minimum_value = 0.1,
    maximum_value = 10.0,
    order = "a-speed-02"
  },
  {
    type = "double-setting",
    name = "cmq-speed-epic",
    setting_type = "startup",
    default_value = 2.0,
    minimum_value = 0.1,
    maximum_value = 10.0,
    order = "a-speed-03"
  },
  {
    type = "double-setting",
    name = "cmq-speed-legendary",
    setting_type = "startup",
    default_value = 3.0,
    minimum_value = 0.1,
    maximum_value = 10.0,
    order = "a-speed-04"
  },
  
  -- Range multipliers for each quality tier
  {
    type = "double-setting",
    name = "cmq-range-uncommon",
    setting_type = "startup",
    default_value = 1.2,
    minimum_value = 0.1,
    maximum_value = 5.0,
    order = "b-range-01"
  },
  {
    type = "double-setting",
    name = "cmq-range-rare",
    setting_type = "startup",
    default_value = 1.5,
    minimum_value = 0.1,
    maximum_value = 5.0,
    order = "b-range-02"
  },
  {
    type = "double-setting",
    name = "cmq-range-epic",
    setting_type = "startup",
    default_value = 2.0,
    minimum_value = 0.1,
    maximum_value = 5.0,
    order = "b-range-03"
  },
  {
    type = "double-setting",
    name = "cmq-range-legendary",
    setting_type = "startup",
    default_value = 2.5,
    minimum_value = 0.1,
    maximum_value = 5.0,
    order = "b-range-04"
  },
  
  -- Power consumption multipliers for each quality tier
  {
    type = "double-setting",
    name = "cmq-power-uncommon",
    setting_type = "startup",
    default_value = 1.1,
    minimum_value = 0.1,
    maximum_value = 5.0,
    order = "c-power-01"
  },
  {
    type = "double-setting",
    name = "cmq-power-rare",
    setting_type = "startup",
    default_value = 1.3,
    minimum_value = 0.1,
    maximum_value = 5.0,
    order = "c-power-02"
  },
  {
    type = "double-setting",
    name = "cmq-power-epic",
    setting_type = "startup",
    default_value = 1.6,
    minimum_value = 0.1,
    maximum_value = 5.0,
    order = "c-power-03"
  },
  {
    type = "double-setting",
    name = "cmq-power-legendary",
    setting_type = "startup",
    default_value = 2.0,
    minimum_value = 0.1,
    maximum_value = 5.0,
    order = "c-power-04"
  },
  
  -- Module slot bonuses for each quality tier
  {
    type = "int-setting",
    name = "cmq-modules-uncommon",
    setting_type = "startup",
    default_value = 0,
    minimum_value = 0,
    maximum_value = 10,
    order = "d-modules-01"
  },
  {
    type = "int-setting",
    name = "cmq-modules-rare",
    setting_type = "startup",
    default_value = 1,
    minimum_value = 0,
    maximum_value = 10,
    order = "d-modules-02"
  },
  {
    type = "int-setting",
    name = "cmq-modules-epic",
    setting_type = "startup",
    default_value = 2,
    minimum_value = 0,
    maximum_value = 10,
    order = "d-modules-03"
  },
  {
    type = "int-setting",
    name = "cmq-modules-legendary",
    setting_type = "startup",
    default_value = 3,
    minimum_value = 0,
    maximum_value = 10,
    order = "d-modules-04"
  }
})
