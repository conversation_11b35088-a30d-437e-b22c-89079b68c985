[mod-setting-name]
cmq-speed-uncommon=Uncommon Speed Multiplier
cmq-speed-rare=Rare Speed Multiplier
cmq-speed-epic=Epic Speed Multiplier
cmq-speed-legendary=Legendary Speed Multiplier

cmq-range-uncommon=Uncommon Range Multiplier
cmq-range-rare=Rare Range Multiplier
cmq-range-epic=Epic Range Multiplier
cmq-range-legendary=Legendary Range Multiplier

cmq-power-uncommon=Uncommon Power Multiplier
cmq-power-rare=Rare Power Multiplier
cmq-power-epic=Epic Power Multiplier
cmq-power-legendary=Legendary Power Multiplier

cmq-modules-uncommon=Uncommon Module Slot Bonus
cmq-modules-rare=Rare Module Slot Bonus
cmq-modules-epic=Epic Module Slot Bonus
cmq-modules-legendary=Legendary Module Slot Bonus

[mod-setting-description]
cmq-speed-uncommon=Target mining speed multiplier for uncommon quality (intermediate values scale automatically)
cmq-speed-rare=Target mining speed multiplier for rare quality (intermediate values scale automatically)
cmq-speed-epic=Target mining speed multiplier for epic quality (intermediate values scale automatically)
cmq-speed-legendary=Target mining speed multiplier for legendary quality (base stats are adjusted to reach this target)

cmq-range-uncommon=Target resource searching radius multiplier for uncommon quality (intermediate values scale automatically)
cmq-range-rare=Target resource searching radius multiplier for rare quality (intermediate values scale automatically)
cmq-range-epic=Target resource searching radius multiplier for epic quality (intermediate values scale automatically)
cmq-range-legendary=Target resource searching radius multiplier for legendary quality (base stats are adjusted to reach this target)

cmq-power-uncommon=Target power consumption multiplier for uncommon quality (intermediate values scale automatically)
cmq-power-rare=Target power consumption multiplier for rare quality (intermediate values scale automatically)
cmq-power-epic=Target power consumption multiplier for epic quality (intermediate values scale automatically)
cmq-power-legendary=Target power consumption multiplier for legendary quality (base stats are adjusted to reach this target)

cmq-modules-uncommon=Target additional module slots for uncommon quality (intermediate values scale automatically)
cmq-modules-rare=Target additional module slots for rare quality (intermediate values scale automatically)
cmq-modules-epic=Target additional module slots for epic quality (intermediate values scale automatically)
cmq-modules-legendary=Target additional module slots for legendary quality (base stats are adjusted to reach this target)
